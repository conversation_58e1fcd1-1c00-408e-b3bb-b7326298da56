<script setup>
import { createVNode, getCurrentInstance, onMounted, ref } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import ApplicationApi from '@/api/llm/application.js'
import FeedbackUtil from '@/utils/feedback.js'

const { proxy } = getCurrentInstance()

const records = ref([])

const baseURL = import.meta.env.VITE_DIFY_BASE_URL

const icon = icon => {
  return `${baseURL}${icon}`
}

const isPath = icon => {
  return typeof icon === 'string' && icon.startsWith('/')
}

const load = () => {
  ApplicationApi.searchUserApplications(null, null, {
    toast: {
      success: false
    }
  }).then(result => {
    records.value = result.data
  })
}

const enter = record => {
  ApplicationApi.installId(record.platformId, {
    toast: {
      success: false
    }
  }).then(result => {
    proxy.$router.push({
      name: 'llm.application.home',
      query: {
        id: result.data
      }
    })
  })
}

const edit = record => {
  if (record === null) {
    // 新建应用，显示类型选择器
    showTypeSelector.value = true
  } else {
    // 编辑现有应用
    proxy.$router.push({
      name: 'llm.application.edit',
      query: {
        id: record.platformId
      }
    })
  }
}

const types = [{
  type: 'dataset',
  title: '知识库对话',
  description: '基于知识库文档进行智能问答',
  icon: 'DatabaseOutlined',
  color: '#1890ff'
}, {
  type: 'workflow',
  title: '工作流',
  description: '创建复杂的AI工作流程',
  icon: 'ApartmentOutlined',
  color: '#52c41a'
}, {
  type: 'prompt',
  title: '提示词',
  description: '基于提示词模板的对话应用',
  icon: 'MessageOutlined',
  color: '#fa8c16'
}, {
  type: 'link',
  title: '外链',
  description: '集成外部应用或服务',
  icon: 'LinkOutlined',
  color: '#eb2f96'
}]

const showTypeSelector = ref(false)

const selectApplicationType = type => {
  showTypeSelector.value = false

  proxy.$router.push({
    name: `llm.application.${type}.edit`
  })
}

const remove = id => {
  FeedbackUtil.modal('您即将删除该记录，是否继续？', 'confirm', {
    icon: createVNode(ExclamationCircleOutlined),
    onOk () {
      const _promise = ApplicationApi.remove(id, {
        showLoading: false
      })
      _promise.then(() => {
        load()
      })

      return _promise
    },
    onCancel () {
      return Promise.resolve()
    }
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <div class="container">
    <a-row :gutter="[16, 16]">
      <!-- 新建应用卡片 -->
      <a-col
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="create-card"
          :hoverable="true"
          @click="showTypeSelector = true"
        >
          <div class="create-card-content">
            <div class="create-icon-wrapper">
              <plus-outlined class="create-icon" />
            </div>
            <h3 class="create-title">
              新建
            </h3>
            <p class="create-description">
              创建一个新的数智人应用
            </p>
          </div>
        </a-card>
      </a-col>

      <!-- 应用卡片列表 -->
      <a-col
        v-for="i in records"
        :key="i.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
        :xl="4"
      >
        <a-card
          class="app-card"
          :hoverable="true"
        >
          <div class="app-card-header">
            <div class="app-avatar-wrapper">
              <template v-if="isPath(i.icon)">
                <a-avatar
                  :src="icon(i.icon)"
                  :size="48"
                />
              </template>
              <template v-else>
                <a-avatar
                  :size="48"
                  class="app-avatar"
                >
                  {{ i.icon }}
                </a-avatar>
              </template>
            </div>
          </div>

          <div class="app-card-content">
            <h3
              class="app-title"
              :title="i.name || '未命名'"
            >
              {{ i.name || '未命名' }}
            </h3>
            <p
              class="app-description"
              :title="i.description || '暂无描述'"
            >
              {{ i.description || '暂无描述' }}
            </p>
            <div class="app-meta">
              <a-space :size="8">
                <a-tag
                  :color="'blue'"
                  :size="'small'"
                >
                  <tag-outlined />
                  暂无标签
                </a-tag>
              </a-space>
            </div>
          </div>

          <div class="app-card-actions">
            <a-tooltip title="对话">
              <a-button
                type="primary"
                shape="circle"
                size="small"
                class="action-btn primary-btn"
                @click="enter(i)"
              >
                <comment-outlined />
              </a-button>
            </a-tooltip>

            <!-- 创建人允许编辑 -->
            <template v-if="i.isOwner">
              <a-tooltip title="编辑">
                <a-button
                  type="default"
                  shape="circle"
                  size="small"
                  class="action-btn"
                  @click="edit(i)"
                >
                  <edit-outlined />
                </a-button>
              </a-tooltip>

              <a-tooltip title="删除">
                <a-button
                  type="default"
                  shape="circle"
                  size="small"
                  class="action-btn danger-btn"
                  @click="remove(i.id)"
                >
                  <delete-outlined />
                </a-button>
              </a-tooltip>
            </template>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 应用类型选择模态框 -->
    <a-modal
      v-model:open="showTypeSelector"
      :title="'选择应用类型'"
      :footer="null"
      :width="800"
      :centered="true"
    >
      <div class="type-selector-content">
        <a-row :gutter="[16, 16]">
          <a-col
            v-for="appType in types"
            :key="appType.type"
            :xs="24"
            :sm="12"
            :md="12"
            :lg="12"
          >
            <a-card
              class="type-card"
              :hoverable="true"
              @click="selectApplicationType(appType.type)"
            >
              <div class="type-card-content">
                <div
                  class="type-icon-wrapper"
                  :style="{ backgroundColor: appType.color + '20' }"
                >
                  <component
                    :is="appType.icon"
                    class="type-icon"
                    :style="{ color: appType.color }"
                  />
                </div>
                <h3 class="type-title">
                  {{ appType.title }}
                </h3>
                <p class="type-description">
                  {{ appType.description }}
                </p>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
@import '@/less/default';

.container {
  padding: @padding-sm;
  min-height: calc(100vh - 64px);
}

// 新建应用卡片
.create-card {
  height: 280px;
  border-radius: 12px;
  border: 2px dashed #d9d9d9;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

    .create-icon {
      color: #1890ff;
    }

    .create-title {
      color: #1890ff;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .create-card-content {
    text-align: center;
  }

  .create-icon-wrapper {
    width: 60px;
    height: 60px;
    margin: 0 auto 16px;
    border-radius: 50%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .create-icon {
    font-size: 24px;
    color: #8c8c8c;
    transition: color 0.3s ease;
  }

  .create-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    transition: color 0.3s ease;
  }

  .create-description {
    font-size: 14px;
    color: #8c8c8c;
    margin: 0;
  }
}

// 应用卡片
.app-card {
  height: 280px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

    .app-card-actions {
      opacity: 1;
    }
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
  }

  .app-card-header {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
  }

  .app-avatar {
    background: rgb(255, 234, 213);
    color: #fff;
    font-size: 20px;
  }

  .app-card-content {
    flex: 1;
    text-align: center;
    margin-bottom: 20px;
  }

  .app-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .app-description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0 0 12px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 36px;
  }

  .app-meta {
    margin-bottom: 8px;

    :deep(.ant-tag) {
      margin: 0;
      border-radius: 8px;
      font-size: 12px;
      padding: 2px 8px;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }
  }

  .app-card-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &.primary-btn {
      background: #1890ff;
      border-color: #1890ff;
      color: #fff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.danger-btn:hover {
      border-color: #ff4d4f;
      color: #ff4d4f;
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .app-card,
  .create-card {
    height: 240px;
  }

  .create-icon-wrapper {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
  }

  .create-icon {
    font-size: 20px;
  }

  .create-title {
    font-size: 14px;
  }

  .app-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 16px;
  }

  .app-title {
    font-size: 14px;
  }

  .app-description {
    font-size: 12px;
  }
}

// 应用类型选择器样式
.type-selector-content {
  padding: 16px 0;
}

.type-card {
  height: 160px;
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    border-color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  :deep(.ant-card-body) {
    height: 100%;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .type-card-content {
    text-align: center;
    width: 100%;
  }

  .type-icon-wrapper {
    width: 48px;
    height: 48px;
    margin: 0 auto 12px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .type-icon {
    font-size: 24px;
  }

  .type-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin: 0 0 8px 0;
  }

  .type-description {
    font-size: 13px;
    color: #8c8c8c;
    margin: 0;
    line-height: 1.4;
  }
}
</style>
