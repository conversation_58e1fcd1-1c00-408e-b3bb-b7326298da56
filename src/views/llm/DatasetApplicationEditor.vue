<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'
import DatasetApi from '@/api/llm/dataset.js'


const { proxy } = getCurrentInstance()

// 表单验证配置 - 使用通用的验证配置
const _config = {
  name: Promise.resolve({ required: true, message: '请输入应用名称' }),
  description: Promise.resolve({ required: true, message: '请输入应用描述' })
}

// 表单字段定义
const fields = ref([
  {
    title: '应用名称',
    field: 'name',
    type: 'text',
    config: {
      promise: _config.name,
      placeholder: '请输入应用名称'
    }
  },
  {
    title: '应用描述',
    field: 'description',
    type: 'textarea',
    config: {
      promise: _config.description,
      placeholder: '请输入应用描述',
      rows: 3
    }
  },
  {
    title: '关联知识库',
    field: 'datasetId',
    type: 'select',
    config: {
      placeholder: '请选择要关联的知识库',
      options: []
    }
  },
  {
    title: '应用图标',
    field: 'icon',
    type: 'text',
    config: {
      placeholder: '请输入图标字符或emoji'
    }
  }
])

// 页面操作
const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

// 表单操作
const actions = ref([
  {
    title: '保存',
    type: 'primary',
    callback(record) {
      const _promise = ApplicationApi.saveDataset({
        ...record,
        platform: 'DIFY'
      })

      _promise.then(result => {
        if (record.id == null) {
          // 新建成功，跳转到应用列表
          proxy.$router.push({ name: 'llm.application.index' })
        } else {
          reloadPage()
        }
      })

      return _promise
    }
  },
  {
    title: '取消',
    callback() {
      proxy.$router.go(-1)
    }
  }
])

// 如果是编辑模式，添加删除按钮
const applicationId = computed(() => proxy.$route.query.id)
if (applicationId.value) {
  actions.value.splice(1, 0, {
    title: '删除',
    type: 'danger',
    callback(record) {
      const _promise = ApplicationApi.remove(record.id)
      
      _promise.then(() => {
        proxy.$router.push({ name: 'llm.application.index' })
      })

      return _promise
    }
  })
}

const form = ref()
const datasets = ref([])

// 加载知识库列表
const loadDatasets = () => {
  DatasetApi.search(100, 0, null, {
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    datasets.value = result.data.records.map(item => ({
      label: item.title,
      value: item.id
    }))

    // 更新表单字段选项
    const datasetField = fields.value.find(f => f.field === 'datasetId')
    if (datasetField) {
      datasetField.config.options = datasets.value
    }
  })
}

onMounted(() => {
  loadDatasets()
  
  if (applicationId.value) {
    // 编辑模式，加载应用数据
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })
  } else {
    // 新建模式，设置默认值
    form.value.setModel({
      icon: '📚'
    })
  }
})
</script>

<template>
  <div class="layout-content-panel">
    <a-card title="知识库对话应用">
      <template #extra>
        <a-space>
          <a-tag color="blue">知识库对话</a-tag>
        </a-space>
      </template>
      
      <div class="editor-content">
        <a-row :gutter="24">
          <a-col :span="16">
            <GenericForm
              ref="form"
              :fields="fields"
              :actions="actions"
              :layout="{
                labelCol: { span: 6 },
                wrapperCol: { span: 18 }
              }"
            />
          </a-col>
          <a-col :span="8">
            <a-card size="small" title="应用说明">
              <div class="help-content">
                <h4>知识库对话应用</h4>
                <p>基于知识库文档进行智能问答的应用类型。</p>
                
                <h5>功能特点：</h5>
                <ul>
                  <li>基于知识库内容回答问题</li>
                  <li>支持文档检索和语义搜索</li>
                  <li>提供准确的信息来源引用</li>
                  <li>适合客服、文档问答等场景</li>
                </ul>
                
                <h5>使用说明：</h5>
                <ol>
                  <li>选择要关联的知识库</li>
                  <li>设置应用名称和描述</li>
                  <li>可自定义应用图标</li>
                  <li>保存后即可开始使用</li>
                </ol>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.layout-content-panel {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

.editor-content {
  margin-top: 16px;
}

.help-content {
  h4 {
    color: #262626;
    margin-bottom: 8px;
  }
  
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }
  
  ul, ol {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;
    
    li {
      margin-bottom: 4px;
    }
  }
}
</style>
