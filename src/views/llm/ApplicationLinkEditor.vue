<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'


const { proxy } = getCurrentInstance()

// 表单验证配置 - 使用通用的验证配置
const _config = {
  name: Promise.resolve({ required: true, message: '请输入应用名称' }),
  description: Promise.resolve({ required: true, message: '请输入应用描述' }),
  url: Promise.resolve({ required: true, message: '请输入外链地址' })
}

// 表单字段定义
const fields = ref([
  {
    title: '应用名称',
    field: 'name',
    type: 'text',
    config: {
      promise: _config.name,
      placeholder: '请输入应用名称'
    }
  },
  {
    title: '应用描述',
    field: 'description',
    type: 'textarea',
    config: {
      promise: _config.description,
      placeholder: '请输入应用描述',
      rows: 3
    }
  },
  {
    title: '外链地址',
    field: 'url',
    type: 'text',
    config: {
      promise: _config.url,
      placeholder: '请输入完整的URL地址，如：https://example.com'
    }
  },
  {
    title: '应用图标',
    field: 'icon',
    type: 'text',
    config: {
      placeholder: '请输入图标字符或emoji'
    }
  },
  {
    title: '打开方式',
    field: 'openType',
    type: 'radio',
    config: {
      options: [
        { label: '当前窗口', value: 'current' },
        { label: '新窗口', value: 'new' },
        { label: '内嵌框架', value: 'iframe' }
      ]
    }
  },
  {
    title: '是否需要认证',
    field: 'requireAuth',
    type: 'switch',
    config: {
      checkedChildren: '是',
      unCheckedChildren: '否'
    }
  }
])

// 页面操作
const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

// 表单操作
const actions = ref([
  {
    title: '保存',
    type: 'primary',
    callback(record) {
      const _promise = ApplicationApi.saveLink({
        ...record,
        platform: 'DIFY'
      })

      _promise.then(result => {
        if (record.id == null) {
          // 新建成功，跳转到应用列表
          proxy.$router.push({ name: 'llm.application.index' })
        } else {
          reloadPage()
        }
      })

      return _promise
    }
  },
  {
    title: '预览',
    callback(record) {
      if (record.url) {
        window.open(record.url, '_blank')
      } else {
        proxy.$message.warning('请先输入外链地址')
      }
    }
  },
  {
    title: '取消',
    callback() {
      proxy.$router.go(-1)
    }
  }
])

// 如果是编辑模式，添加删除按钮
const applicationId = computed(() => proxy.$route.query.id)
if (applicationId.value) {
  actions.value.splice(2, 0, {
    title: '删除',
    type: 'danger',
    callback(record) {
      const _promise = ApplicationApi.remove(record.id)
      
      _promise.then(() => {
        proxy.$router.push({ name: 'llm.application.index' })
      })

      return _promise
    }
  })
}

const form = ref()

// 常用外链示例
const linkExamples = [
  {
    title: 'ChatGPT',
    url: 'https://chat.openai.com',
    description: 'OpenAI ChatGPT官方网站',
    icon: '🤖'
  },
  {
    title: 'Claude',
    url: 'https://claude.ai',
    description: 'Anthropic Claude AI助手',
    icon: '🧠'
  },
  {
    title: 'Midjourney',
    url: 'https://www.midjourney.com',
    description: 'AI图像生成工具',
    icon: '🎨'
  },
  {
    title: '文心一言',
    url: 'https://yiyan.baidu.com',
    description: '百度文心一言',
    icon: '💭'
  }
]

// 使用示例
const useExample = (example) => {
  const currentModel = form.value.getModel()
  form.value.setModel({
    ...currentModel,
    name: example.title,
    description: example.description,
    url: example.url,
    icon: example.icon
  })
}

// URL验证
const validateUrl = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

onMounted(() => {
  if (applicationId.value) {
    // 编辑模式，加载应用数据
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })
  } else {
    // 新建模式，设置默认值
    form.value.setModel({
      icon: '🔗',
      openType: 'new',
      requireAuth: false
    })
  }
})
</script>

<template>
  <div class="layout-content-panel">
    <a-card title="外链应用">
      <template #extra>
        <a-space>
          <a-tag color="pink">外链</a-tag>
        </a-space>
      </template>
      
      <div class="editor-content">
        <a-row :gutter="24">
          <a-col :span="16">
            <GenericForm
              ref="form"
              :fields="fields"
              :actions="actions"
              :layout="{
                labelCol: { span: 6 },
                wrapperCol: { span: 18 }
              }"
            />
          </a-col>
          <a-col :span="8">
            <a-space direction="vertical" style="width: 100%;" :size="16">
              <!-- 应用说明 -->
              <a-card size="small" title="应用说明">
                <div class="help-content">
                  <h4>外链应用</h4>
                  <p>集成外部应用或服务，通过链接快速访问第三方工具。</p>
                  
                  <h5>功能特点：</h5>
                  <ul>
                    <li>快速集成外部服务</li>
                    <li>支持多种打开方式</li>
                    <li>可配置认证要求</li>
                    <li>统一应用入口管理</li>
                  </ul>
                  
                  <h5>打开方式说明：</h5>
                  <ul>
                    <li><strong>当前窗口</strong>：在当前页面打开链接</li>
                    <li><strong>新窗口</strong>：在新标签页打开链接</li>
                    <li><strong>内嵌框架</strong>：在应用内以iframe形式显示</li>
                  </ul>
                </div>
              </a-card>

              <!-- 常用外链示例 -->
              <a-card size="small" title="常用外链">
                <a-space direction="vertical" style="width: 100%;" :size="8">
                  <div
                    v-for="example in linkExamples"
                    :key="example.title"
                    class="example-item"
                  >
                    <div class="example-header">
                      <span class="example-icon">{{ example.icon }}</span>
                      <span class="example-title">{{ example.title }}</span>
                      <a-button 
                        size="small" 
                        type="link"
                        @click="useExample(example)"
                      >
                        使用
                      </a-button>
                    </div>
                    <div class="example-description">{{ example.description }}</div>
                    <div class="example-url">{{ example.url }}</div>
                  </div>
                </a-space>
              </a-card>
            </a-space>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.layout-content-panel {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

.editor-content {
  margin-top: 16px;
}

.help-content {
  h4 {
    color: #262626;
    margin-bottom: 8px;
  }
  
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }
  
  ul {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;
    
    li {
      margin-bottom: 4px;
      
      strong {
        color: #595959;
      }
    }
  }
}

.example-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  
  .example-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 8px;
  }
  
  .example-icon {
    font-size: 16px;
  }
  
  .example-title {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
    flex: 1;
  }
  
  .example-description {
    color: #8c8c8c;
    font-size: 12px;
    margin-bottom: 2px;
  }
  
  .example-url {
    color: #1890ff;
    font-size: 11px;
    font-family: monospace;
  }
}
</style>
