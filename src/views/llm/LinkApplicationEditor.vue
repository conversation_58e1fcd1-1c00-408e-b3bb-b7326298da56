<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.si.dubhe.model.LinkApplication', ['name', 'description', 'link', 'target'])

// 表单字段定义
const fields = ref([{
  title: '应用名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '目标地址',
  field: 'link',
  type: 'textarea',
  config: {
    promise: _config.link
  }
}, {
  title: '打开方式',
  field: 'target',
  type: 'radio',
  config: {
    promise: new Promise(resolve => {
      _config.target.then(data => {
        data.options = [{
          label: '新标签页',
          value: 'SELF'
        }, {
          label: '新窗口',
          value: 'BLANK'
        }]

        resolve(data)
      })
    })
  }
}])

// 页面操作
const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

// 表单操作
const actions = [{
  title: '保存',
  callback (record) {
    const _promise = ApplicationApi.saveLink(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  title: '取消',
  callback () {
    closePage(proxy.$route.path)
  }
}]

const applicationId = computed(() => proxy.$route.query.id)

const form = ref()

onMounted(() => {
  if (applicationId.value) {
    // 编辑模式，加载应用数据
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })
  } else {
    form.value.setModel({
    })
  }
})
</script>

<template>
  <a-card :title="'外链应用'">
    <a-row :gutter="24">
      <a-col :span="8">
        <a-card
          :size="'small'"
          :title="'应用说明'"
        >
          <div class="help-content">
            <p>集成外部应用或服务，通过链接快速访问第三方工具。</p>

            <h5>功能特点：</h5>
            <ul>
              <li>统一应用入口管理</li>
              <li>快速集成外部服务</li>
              <li>支持多种打开方式</li>
            </ul>

            <h5>打开方式说明：</h5>
            <ul>
              <li><strong>新标签页</strong>：在应用的新标签页中打开链接</li>
              <li><strong>新窗口</strong>：在浏览器的新标签页打开链接</li>
            </ul>
          </div>
        </a-card>
      </a-col>

      <a-col :span="16">
        <GenericForm
          ref="form"
          :layout="{
            showLabel: true,
            labelCol: {
              sm: {
                span: 7
              },
              lg: {
                span: 4
              }
            },
            wrapperCol: {
              sm: {
                span: 17
              },
              lg: {
                span: 20
              }
            }
          }"
          :fields="fields"
          :actions="actions"
        />
      </a-col>
    </a-row>
  </a-card>
</template>

<style lang="less" scoped>
.layout-content-panel {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

.editor-content {
  margin-top: 16px;
}

.help-content {
  h4 {
    color: #262626;
    margin-bottom: 8px;
  }

  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }

  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  ul {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;

    li {
      margin-bottom: 4px;

      strong {
        color: #595959;
      }
    }
  }
}

.example-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;

  .example-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    gap: 8px;
  }

  .example-icon {
    font-size: 16px;
  }

  .example-title {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
    flex: 1;
  }

  .example-description {
    color: #8c8c8c;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .example-url {
    color: #1890ff;
    font-size: 11px;
    font-family: monospace;
  }
}
</style>
