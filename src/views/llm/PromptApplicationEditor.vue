<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.si.dubhe.model.PromptApplication', ['name', 'description', 'prompt'])

// 表单字段定义
const fields = ref([{
  title: '应用名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: 'SystemPrompt',
  field: 'prompt',
  type: 'textarea',
  config: {
    promise: _config.prompt
  }
}])

// 页面操作
const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

// 表单操作
const actions = ref([{
  title: '保存',
  callback (record) {
    const _promise = ApplicationApi.savePrompt(record)

    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  title: '取消',
  callback () {
    closePage(proxy.$route.path)
  }
}])

const applicationId = computed(() => proxy.$route.query.id)

const form = ref()

const examples = [{
  title: '客服助手',
  prompt: '你是一个专业的客服助手。请根据用户的问题：{{question}}，提供准确、友好的回答。回答要简洁明了，语气要礼貌专业。'
}, {
  title: '翻译助手',
  prompt: '请将以下{{source_language}}文本翻译成{{target_language}}：\n\n{{text}}\n\n要求：\n1. 翻译要准确、自然\n2. 保持原文的语气和风格\n3. 如有专业术语，请保持准确性'
}, {
  title: '写作助手',
  prompt: '请根据以下要求写一篇{{type}}：\n\n主题：{{topic}}\n要求：{{requirements}}\n\n请确保内容结构清晰、逻辑严谨、语言流畅。'
}]

const useExample = example => {
  const _model = form.value.getModel()
  form.value.setModel({
    ..._model,
    name: example.title,
    prompt: example.prompt
  })
}

onMounted(() => {
  if (applicationId.value) {
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })
  } else {
    form.value.setModel({
    })
  }
})
</script>

<template>
  <a-card :title="'提示词应用'">
    <div class="editor-content">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-space
            :direction="'vertical'"
            style="width: 100%;"
          >
            <a-card
              :size="'small'"
              :title="'应用说明'"
            >
              <div class="help-content">
                <p>基于提示词模板的对话应用，支持变量替换。</p>

                <h5>功能特点：</h5>
                <ul>
                  <li>自定义提示词模板</li>
                  <li>支持变量参数化</li>
                </ul>

                <h5>变量语法：</h5>
                <p>使用 <code>{{ 变量名 }}</code> 定义变量，用户输入时会要求填写对应值。</p>
              </div>
            </a-card>

            <a-card
              :size="'small'"
              :title="'模板示例'"
            >
              <a-space
                :direction="'vertical'"
                style="width: 100%;"
              >
                <div
                  v-for="i in examples"
                  :key="i.title"
                  class="example-item"
                >
                  <div class="example-header">
                    <span class="example-title">{{ i.title }}</span>
                    <a-button
                      :size="'small'"
                      :type="'link'"
                      @click="useExample(i)"
                    >
                      使用
                    </a-button>
                  </div>
                  <div class="example-prompt">
                    {{ i.prompt.substring(0, 100) }}...
                  </div>
                </div>
              </a-space>
            </a-card>
          </a-space>
        </a-col>

        <a-col :span="16">
          <GenericForm
            ref="form"
            :layout="{
              showLabel: true,
              labelCol: {
                sm: {
                  span: 7
                },
                lg: {
                  span: 4
                }
              },
              wrapperCol: {
                sm: {
                  span: 17
                },
                lg: {
                  span: 20
                }
              }
            }"
            :fields="fields"
            :actions="actions"
          />
        </a-col>
      </a-row>
    </div>
  </a-card>
</template>

<style lang="less" scoped>
.editor-content {
  margin-top: 16px;
}

.help-content {
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }

  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  ul {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;

    li {
      margin-bottom: 4px;
    }
  }

  code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
  }
}

.example-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;

  .example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .example-title {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
  }

  .example-prompt {
    color: #8c8c8c;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>
