<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'


const { proxy } = getCurrentInstance()

// 表单验证配置 - 使用通用的验证配置
const _config = {
  name: Promise.resolve({ required: true, message: '请输入应用名称' }),
  description: Promise.resolve({ required: true, message: '请输入应用描述' }),
  prompt: Promise.resolve({ required: true, message: '请输入提示词模板' })
}

// 表单字段定义
const fields = ref([
  {
    title: '应用名称',
    field: 'name',
    type: 'text',
    config: {
      promise: _config.name,
      placeholder: '请输入应用名称'
    }
  },
  {
    title: '应用描述',
    field: 'description',
    type: 'textarea',
    config: {
      promise: _config.description,
      placeholder: '请输入应用描述',
      rows: 3
    }
  },
  {
    title: '提示词模板',
    field: 'prompt',
    type: 'textarea',
    config: {
      promise: _config.prompt,
      placeholder: '请输入提示词模板，可使用 {{变量名}} 定义变量',
      rows: 8
    }
  },
  {
    title: '应用图标',
    field: 'icon',
    type: 'text',
    config: {
      placeholder: '请输入图标字符或emoji'
    }
  },
  {
    title: '模型温度',
    field: 'temperature',
    type: 'slider',
    config: {
      min: 0,
      max: 1,
      step: 0.1,
      marks: {
        0: '保守',
        0.5: '平衡',
        1: '创新'
      }
    }
  },
  {
    title: '最大回复长度',
    field: 'maxTokens',
    type: 'number',
    config: {
      min: 100,
      max: 4000,
      placeholder: '请输入最大回复长度'
    }
  }
])

// 页面操作
const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

// 表单操作
const actions = ref([
  {
    title: '保存',
    type: 'primary',
    callback(record) {
      const _promise = ApplicationApi.savePrompt({
        ...record,
        platform: 'DIFY'
      })

      _promise.then(result => {
        if (record.id == null) {
          // 新建成功，跳转到应用列表
          proxy.$router.push({ name: 'llm.application.index' })
        } else {
          reloadPage()
        }
      })

      return _promise
    }
  },
  {
    title: '取消',
    callback() {
      proxy.$router.go(-1)
    }
  }
])

// 如果是编辑模式，添加删除按钮
const applicationId = computed(() => proxy.$route.query.id)
if (applicationId.value) {
  actions.value.splice(1, 0, {
    title: '删除',
    type: 'danger',
    callback(record) {
      const _promise = ApplicationApi.remove(record.id)
      
      _promise.then(() => {
        proxy.$router.push({ name: 'llm.application.index' })
      })

      return _promise
    }
  })
}

const form = ref()

// 提示词模板示例
const promptExamples = [
  {
    title: '客服助手',
    prompt: '你是一个专业的客服助手。请根据用户的问题：{{question}}，提供准确、友好的回答。回答要简洁明了，语气要礼貌专业。'
  },
  {
    title: '翻译助手',
    prompt: '请将以下{{source_language}}文本翻译成{{target_language}}：\n\n{{text}}\n\n要求：\n1. 翻译要准确、自然\n2. 保持原文的语气和风格\n3. 如有专业术语，请保持准确性'
  },
  {
    title: '写作助手',
    prompt: '请根据以下要求写一篇{{type}}：\n\n主题：{{topic}}\n要求：{{requirements}}\n\n请确保内容结构清晰、逻辑严谨、语言流畅。'
  }
]

// 使用示例模板
const useExample = (example) => {
  const currentModel = form.value.getModel()
  form.value.setModel({
    ...currentModel,
    name: example.title,
    prompt: example.prompt
  })
}

onMounted(() => {
  if (applicationId.value) {
    // 编辑模式，加载应用数据
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      form.value.setModel(result.data)
    })
  } else {
    // 新建模式，设置默认值
    form.value.setModel({
      icon: '💬',
      temperature: 0.7,
      maxTokens: 1000
    })
  }
})
</script>

<template>
  <div class="layout-content-panel">
    <a-card title="提示词应用">
      <template #extra>
        <a-space>
          <a-tag color="orange">提示词</a-tag>
        </a-space>
      </template>
      
      <div class="editor-content">
        <a-row :gutter="24">
          <a-col :span="16">
            <GenericForm
              ref="form"
              :fields="fields"
              :actions="actions"
              :layout="{
                labelCol: { span: 6 },
                wrapperCol: { span: 18 }
              }"
            />
          </a-col>
          <a-col :span="8">
            <a-space direction="vertical" style="width: 100%;" :size="16">
              <!-- 应用说明 -->
              <a-card size="small" title="应用说明">
                <div class="help-content">
                  <h4>提示词应用</h4>
                  <p>基于提示词模板的对话应用，支持变量替换。</p>
                  
                  <h5>功能特点：</h5>
                  <ul>
                    <li>自定义提示词模板</li>
                    <li>支持变量参数化</li>
                    <li>可调节模型参数</li>
                    <li>适合特定场景对话</li>
                  </ul>
                  
                  <h5>变量语法：</h5>
                  <p>使用 <code>{{变量名}}</code> 定义变量，用户输入时会要求填写对应值。</p>
                </div>
              </a-card>

              <!-- 模板示例 -->
              <a-card size="small" title="模板示例">
                <a-space direction="vertical" style="width: 100%;" :size="8">
                  <div
                    v-for="example in promptExamples"
                    :key="example.title"
                    class="example-item"
                  >
                    <div class="example-header">
                      <span class="example-title">{{ example.title }}</span>
                      <a-button 
                        size="small" 
                        type="link"
                        @click="useExample(example)"
                      >
                        使用
                      </a-button>
                    </div>
                    <div class="example-prompt">{{ example.prompt.substring(0, 100) }}...</div>
                  </div>
                </a-space>
              </a-card>
            </a-space>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<style lang="less" scoped>
.layout-content-panel {
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

.editor-content {
  margin-top: 16px;
}

.help-content {
  h4 {
    color: #262626;
    margin-bottom: 8px;
  }
  
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }
  
  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }
  
  ul {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;
    
    li {
      margin-bottom: 4px;
    }
  }
  
  code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
  }
}

.example-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 8px;
  
  .example-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }
  
  .example-title {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
  }
  
  .example-prompt {
    color: #8c8c8c;
    font-size: 12px;
    line-height: 1.4;
  }
}
</style>
