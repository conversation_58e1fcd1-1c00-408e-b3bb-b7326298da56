<script setup>
import { defineProps, onMounted, ref } from 'vue'
import dayjs from 'dayjs'
import GenericForm from '@/components/GenericForm.vue'
import MediaApi from '@/api/media/record.js'
import FileUtil from '@/utils/file.js'
import UtilApi from '@/api/util.js'
import '@/utils/date.js'

const props = defineProps({
  get: {
    type: Function,
    default: null
  },
  save: {
    type: Function,
    default: null
  }
})

const avatar = ref({
  id: null,
  src: null
})

// 缩放图片
const zoomAvatar = file => {
  return FileUtil.zoomImage(file, {
    max: 300
  }).then(base64 => {
    const _img = document.createElement('img')
    _img.src = base64

    return new Promise(resolve => {
      _img.onload = () => {
        const _canvas = document.createElement('canvas')
        _canvas.width = _img.naturalWidth
        _canvas.height = _img.naturalHeight
        _canvas.getContext('2d').drawImage(_img, 0, 0)
        _canvas.toBlob(resolve)
      }
    })
  })
}

const uploadAvatar = ({ action, data, file, filename, headers, onError, onProgress, onSuccess, withCredentials }) => {
  MediaApi.uploadForm('avatar', [file], {
    toast: {
      success: false
    }
  }).then(result => {
    if (result.data[0].success) {
      avatar.value.id = result.data[0].id
      avatar.value.src = MediaApi.preview(result.data[0].id)
    }
  })
}

const form = ref()
const fields = ref([{
  title: '姓名',
  field: 'name',
  type: 'text',
  config: {
    disabled: true
  }
}, {
  title: '登录账号',
  field: 'account',
  type: 'text',
  config: {
    disabled: true
  }
}, {
  title: '部门',
  field: 'department',
  type: 'organization',
  config: {
    disabled: true,
    max: 1,
    type: 'department',
    rules: [{
      required: true,
      message: '请选择归属部门'
    }]
  }
}, {
  title: '汉语拼音',
  field: 'alphabet',
  type: 'text',
  config: {
    disabled: true
  }
}, {
  title: '手机号码',
  field: 'mp',
  type: 'number'
}, {
  title: '电子邮箱',
  field: 'email',
  type: 'text',
  config: {
    rules: [{
      type: 'email',
      message: '请输入有效的电子邮箱'
    }]
  }
}, {
  title: '生日',
  field: 'birthday',
  type: 'datetime',
  config: {
    format: 'YYYY-MM-DD',
    showTime: false
  }
}, {
  title: '住址',
  field: 'address',
  type: 'location'
}, {
  title: '性别',
  field: 'isMale',
  type: 'select',
  config: {
    options: [{
      label: '男性',
      value: true
    }, {
      label: '女性',
      value: false
    }]
  }
}])

defineExpose({
  fields
})

const actions = [{
  icon: 'SaveOutlined',
  title: '保存',
  callback (model) {
    const _model = {
      ...model
    }

    // 保存部门
    _model.deptId = Array.isArray(model.department) && model.department.length > 0 ? model.department[0].id : null

    // 保存头像
    _model.avatarId = avatar.value.id

    return props.save(_model)
  }
}]

const ready = ref(false)
onMounted(() => {
  const _config = UtilApi.validation('com.chinamobile.sparrow.domain.model.sys.User', ['name', 'account', 'alphabet', 'mp', 'email', 'address'])

  const _temp = []
  for (const prop in _config) {
    const _fields = fields.value.filter(i => i.field === prop)
    if (_fields.length === 0) {
      continue
    }

    _fields[0].config = _fields[0].config ? _fields[0].config : {}
    _fields[0].config.promise = _config[prop]

    _temp.push(_config[prop])
  }

  Promise.all(_temp)
    .then(() => {
      props.get().then(result => {
        if (result.data === null) {
          return
        }

        // 设置部门
        result.data.department = [{
          id: result.data.deptId,
          name: result.data.deptName,
          code: result.data.deptCode,
          fullName: result.data.fullName
        }]

        // 设置头像
        if (result.data.avatarId !== null) {
          avatar.value.id = result.data.avatarId
          avatar.value.src = MediaApi.preview(result.data.avatarId)
        }

        // 转换生日
        if (result.data.birthday !== null) {
          result.data.birthday = dayjs(result.data.birthday)
        }

        form.value.setModel(result.data)
      })
    })
    .finally(() => {
      ready.value = true
    })
})
</script>

<template>
  <a-row>
    <!-- 头像 -->
    <a-col
      :xl="12"
      :xs="24"
      class="account-avatar"
    >
      <a-space>
        <a-avatar
          :size="144"
          :src="avatar.src"
        >
          <template #icon>
            <user-outlined />
          </template>
        </a-avatar>
        <a-upload
          :custom-request="uploadAvatar"
          :before-upload="zoomAvatar"
          :max-count="1"
          :show-upload-list="false"
        >
          <a-button>
            <upload-outlined />
            上传头像
          </a-button>
        </a-upload>
      </a-space>
    </a-col>

    <!-- 表单 -->
    <template v-if="ready">
      <a-col
        :xl="12"
        :xs="24"
      >
        <GenericForm
          ref="form"
          :actions="actions"
          :fields="fields"
          :layout="'vertical'"
          :label-col="null"
          :wrapper-col="null"
        />
      </a-col>
    </template>
  </a-row>
</template>

<style lang="less" scoped>
@import '@/less/default';

.account-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;

  .ant-avatar {
    margin-bottom: @margin-sm;
  }
}
</style>
