<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用类型选择功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f2f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #262626;
            margin-bottom: 8px;
        }
        .header p {
            color: #8c8c8c;
            font-size: 16px;
        }
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-section h2 {
            color: #262626;
            margin-top: 0;
            margin-bottom: 16px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 8px;
        }
        .type-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        .type-card {
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .type-card:hover {
            border-color: #1890ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        }
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 24px;
        }
        .type-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin: 0 0 8px 0;
        }
        .type-description {
            font-size: 13px;
            color: #8c8c8c;
            margin: 0;
            line-height: 1.4;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-item {
            border-left: 4px solid #1890ff;
            padding-left: 16px;
        }
        .feature-item h3 {
            color: #262626;
            margin: 0 0 8px 0;
            font-size: 16px;
        }
        .feature-item p {
            color: #8c8c8c;
            margin: 0;
            line-height: 1.5;
        }
        .code-block {
            background: #f5f5f5;
            border-radius: 6px;
            padding: 16px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin: 16px 0;
        }
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }
        .tag-blue { background: #e6f7ff; color: #1890ff; }
        .tag-green { background: #f6ffed; color: #52c41a; }
        .tag-orange { background: #fff7e6; color: #fa8c16; }
        .tag-pink { background: #fff0f6; color: #eb2f96; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>应用类型选择功能演示</h1>
            <p>新建应用时，用户可以选择不同类型的应用进行创建</p>
        </div>

        <div class="demo-section">
            <h2>🎯 应用类型选择</h2>
            <p>点击"新建"按钮后，会弹出应用类型选择模态框，包含以下4种类型：</p>
            
            <div class="type-grid">
                <div class="type-card" onclick="showAlert('知识库对话')">
                    <div class="type-icon" style="background: #e6f7ff; color: #1890ff;">📚</div>
                    <h3 class="type-title">知识库对话</h3>
                    <p class="type-description">基于知识库文档进行智能问答</p>
                    <span class="tag tag-blue">知识库</span>
                </div>
                
                <div class="type-card" onclick="showAlert('工作流')">
                    <div class="type-icon" style="background: #f6ffed; color: #52c41a;">🏢</div>
                    <h3 class="type-title">工作流</h3>
                    <p class="type-description">创建复杂的AI工作流程</p>
                    <span class="tag tag-green">工作流</span>
                </div>
                
                <div class="type-card" onclick="showAlert('提示词')">
                    <div class="type-icon" style="background: #fff7e6; color: #fa8c16;">💬</div>
                    <h3 class="type-title">提示词</h3>
                    <p class="type-description">基于提示词模板的对话应用</p>
                    <span class="tag tag-orange">提示词</span>
                </div>
                
                <div class="type-card" onclick="showAlert('外链')">
                    <div class="type-icon" style="background: #fff0f6; color: #eb2f96;">🔗</div>
                    <h3 class="type-title">外链</h3>
                    <p class="type-description">集成外部应用或服务</p>
                    <span class="tag tag-pink">外链</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>⚡ 功能特点</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>直观的类型选择</h3>
                    <p>使用卡片式布局展示不同应用类型，每种类型都有专门的图标和功能说明</p>
                </div>
                <div class="feature-item">
                    <h3>专业的编辑器</h3>
                    <p>每种应用类型都有专门的编辑器，包含该类型应用所需的所有配置选项</p>
                </div>
                <div class="feature-item">
                    <h3>完整的功能</h3>
                    <p>支持应用的创建、编辑、删除等完整的CRUD操作</p>
                </div>
                <div class="feature-item">
                    <h3>良好的扩展性</h3>
                    <p>模块化设计，便于后续添加新的应用类型和功能</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ 技术实现</h2>
            <p>主要涉及以下文件的修改和新增：</p>
            
            <h3>路由配置</h3>
            <div class="code-block">
// 新增的路由配置
{
  name: 'llm.application.edit.dataset',
  path: '/llm/application/edit/dataset',
  component: () => import('@/views/llm/ApplicationDatasetEditor.vue')
},
{
  name: 'llm.application.edit.prompt',
  path: '/llm/application/edit/prompt', 
  component: () => import('@/views/llm/ApplicationPromptEditor.vue')
},
{
  name: 'llm.application.edit.link',
  path: '/llm/application/edit/link',
  component: () => import('@/views/llm/ApplicationLinkEditor.vue')
}
            </div>

            <h3>API调用</h3>
            <div class="code-block">
// 不同类型应用的保存接口
ApplicationApi.saveDataset(record)   // 知识库对话应用
ApplicationApi.savePrompt(record)    // 提示词应用
ApplicationApi.saveLink(record)      // 外链应用
ApplicationApi.saveWorkflow(record)  // 工作流应用
            </div>
        </div>

        <div class="demo-section">
            <h2>📋 使用流程</h2>
            <ol style="line-height: 1.8; color: #595959;">
                <li>用户访问应用列表页面 <code>/llm/applications</code></li>
                <li>点击"新建"卡片，弹出应用类型选择模态框</li>
                <li>从4种类型中选择一种：知识库对话、工作流、提示词、外链</li>
                <li>根据选择的类型，跳转到对应的编辑器页面</li>
                <li>填写应用的基本信息和特定配置</li>
                <li>点击保存按钮，创建新应用</li>
                <li>自动跳转回应用列表页面</li>
            </ol>
        </div>
    </div>

    <script>
        function showAlert(type) {
            alert(`您选择了：${type}\n\n在实际应用中，这里会跳转到对应的编辑器页面。`);
        }
    </script>
</body>
</html>
